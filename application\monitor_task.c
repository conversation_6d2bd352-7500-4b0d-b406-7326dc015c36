/**
  ****************************(C) COPYRIGHT 2024 WPX****************************
  * @file       rx_task.c/h
  * @brief      read rx data from WP_Rx
  * @note
  * @history
  *  Version    Date            Author          Modification
  *  V1.0.0     Nov-11-2022     TJQ              1. done
  *
  @verbatim
  ==============================================================================

  ==============================================================================
  @endverbatim
  ****************************(C) COPYRIGHT 2024 WPX****************************
  */

#include "cmsis_os.h"
#include "data_task.h"
#include "usb_task.h"
#include "test_task.h"
#include "monitor_task.h"
#include "power_task.h"
#include "online_qi_set.h"
#include "main.h"
#include <stdio.h>
#include <stdarg.h>
#include "string.h"
#include "i2c.h"
#include "bsp_i2c.h"
#include "elog.h"
#include "usart.h"
#include "bsp_MB85RC16.h"
#include "tim.h"
#include "ui.h"
#include "lvgl_control.h"
#include "ntc.h"
#include "key.h"
#include "music_task.h"
#include "ui_fun.h"
#include "usb_device.h"
#include "usbd_cdc_if.h"
#include "ui_Screen_fod_set.h"
#include "FreeRTOS.h"
#include "task.h"
#include "../Middlewares/LVGL/UI/screens/ui_nego_power_popup.h"
#include "auto_detect_test.h"
#include "online_qi_set.h"
#include "EPP_authen.h"
#include "protocol.h"
#include "protocol_send_rec_pack.h"
#include "private_protocol.h"
#include "temperature_monitor.h"
#include "auto_qi_test.h"


uint16_t ADC_num;
uint16_t value;
uint16_t real_temp;
uint8_t enter_private;
bool rx_status;
struct INT_FLAG int_flag_data;
uint8_t Protocol_start = 0;
uint32_t vrect_maintain_time;
uint16_t Vrect_adc_raw_data = 0;
struct USB_status USB_condition;
static volatile uint8_t stop_cep_flag = 0;

bool get_rx_status(void)
{
    return rx_status;
}

uint32_t monitor_timeout(uint32_t moni_timeout)
{
    uint32_t moni_timesout;
    moni_timesout = moni_timeout / MONITOR_OS_DELAY_TIME;
    // log_a("moni_timesout = %d\n",moni_timesout);
    return moni_timesout;
}

void get_button_set_buzzer(void)
{
    if (button_toggle_event() == MUSIC_START) {
        // 使用优化后的按键音效
        send_music(MUSCI_TYPE_BUTTON, mb85rc16_sereen_S.buzzer_button_setting);
        set_button_toggle(MUSIC_STOP);
    }
}



void set_txdata_flag(uint8_t flag)
{
    int_flag_data.int_tx_data = flag;
}
uint8_t get_txdata_flag(void)
{
    return int_flag_data.int_tx_data;
}

/**
 * @brief 掉电保护设置函数
 * @return null
 */

void PVD_config(void)
{
    // 配置PWR
    PWR_PVDTypeDef sConfigPVD;
    sConfigPVD.PVDLevel = PWR_PVDLEVEL_7;         // 低于2.9V触发掉电中断
    sConfigPVD.Mode     = PWR_PVD_MODE_IT_RISING; // 掉电后PVDO会置一，因此选择上升沿触发
    HAL_PWR_ConfigPVD(&sConfigPVD);               // HAL库配置PVD函数

    // 使能PVD
    HAL_PWR_EnablePVD(); // 开启掉电中断
}

/**
 * @brief 掉电保护回调函数
 * @return null
 */

void HAL_PWR_PVDCallback(void)
{
    if (mb85rc16_sereen_S.monitor_switch == MONITOR_OPEN && get_monitor_flag() == 1) {
        mb85rc16_sereen_S.monitor_Power_down_times++;
        MB85RC16_Write_16bit_byte(MONITOR_POWER_DOWN_TIMES, mb85rc16_sereen_S.monitor_Power_down_times);
    }
    // mb85rc16_sereen_S.bootload_loss_power = MB85RC16_Read_8bit_byte(BOOTLOAD_LOSS_POWER);
    // log_e("bootload set = %d,%d\n",mb85rc16_sereen_S.bootload_loss_power,get_Auto_detect_screen());
    if (mb85rc16_sereen_S.bootload_loss_power != COUNT_ENABLE) // 判断bootload是否开启掉电保护，否则由app记录
    {
        // auto_qi_detect_process();
    }
}

void USB_insert_rx_disconnect_times(void)
{
    static uint8_t rx_connect_status = 0;
    if (get_vout_value() > 50 && get_vout_value() < 20000 && rx_connect_status == 0 && get_vrect_status() == TRUE) {
        rx_connect_status = 1;
    }

    if (get_vrect_value() > 50 && get_vrect_value() < 20000 && rx_connect_status == 0 && get_vrect_status() == TRUE) {
        rx_connect_status = 1;
    } else if (get_vrect_status() == FALSE && rx_connect_status == 1) {
        if (mb85rc16_sereen_S.monitor_switch == MONITOR_OPEN && get_monitor_flag() == 1) // 断电次数监控
        {
            mb85rc16_sereen_S.monitor_Power_down_times++;
            MB85RC16_Write_16bit_byte(MONITOR_POWER_DOWN_TIMES, mb85rc16_sereen_S.monitor_Power_down_times);
        }
        if (mb85rc16_sereen_S.Auto_Qi_Detect_Switch == AUTO_DETECT_SWITCH_START) // 自动化测试断电次数记录
        {
            mb85rc16_sereen_S.auto_power_down_times++;
            MB85RC16_Write_16bit_byte(AUTO_TEST_POWER_DOWN_TIMES, mb85rc16_sereen_S.auto_power_down_times); // 这里是最终设置掉电次数
            // printf("mb85rc16_sereen_S.auto_power_down_times = %d",mb85rc16_sereen_S.auto_power_down_times);
        }
        rx_connect_status = 0;
    }
}

void monitor_power_down_buzzer(void)
{
    static uint16_t music_interval;
    if (mb85rc16_sereen_S.monitor_switch == MONITOR_OPEN && get_monitor_flag() == 1 &&
        mb85rc16_sereen_S.monitor_Power_down_times > 0) {
        if (get_vrect_status() == TRUE) // 当RX启动后再开始计算时间
        {
            music_interval++;
        }
        if (music_interval > monitor_timeout(1000)) {
            // 使用优化后的失败音效
            send_music(MUSIC_TYPE_FAIL, mb85rc16_sereen_S.buzzer_Prompt_tone);
            music_interval = 0;
        }
    }
}

void set_epp_nego_power(void)
{
    mb85rc16_sereen_S.screen_page6_epp_nego_power = MB85RC16_Read_8bit_byte(EPP_NEGO_POWER);
    mb85rc16_sereen_S.screen_page6_epp_set        = MB85RC16_Read_8bit_byte(EPP_SET);
    // log_a("screen_page6_epp_nego_power = %d,%d\n",mb85rc16_sereen_S.screen_page6_epp_nego_power,
    //  mb85rc16_sereen_S.screen_page6_epp_set);
    if (mode_switch_detect() == SW_EPP_MODE && mb85rc16_sereen_S.screen_page6_epp_set == EPP_OPERATION) {
        if (mb85rc16_sereen_S.screen_page6_epp_nego_power > 0 && mb85rc16_sereen_S.screen_page6_epp_nego_power <= 30) {
            WP_Rx_set_Power_Level(mb85rc16_sereen_S.screen_page6_epp_nego_power);
            // log_a("option set");
        } else {
            WP_Rx_set_Power_Level(30); // 15W
            // log_a("auto set");
        }
    }
}

void close_power(void) // 关闭负载
{
    HAL_GPIO_WritePin(LOAD_STOP_GPIO_Port, LOAD_STOP_Pin, GPIO_PIN_SET);
    // __HAL_TIM_SET_COMPARE(&htim2, TIM_CHANNEL_3,0);
}

void Stop_Charge(void)
{
    uint8_t i;
    for (i = 0; i < 3; i++) {
        WP_Rx_send_EPT(0x0B);
        osDelay(1);
        WP_set_stop_CEP();
    }
}

void adc_detect_vrect_value(void)
{
    uint16_t Vrect_adc     = 0;
    uint16_t Vrect_adc_sum = 0;
    for (int i = 0; i < ADC_SAMPLE_COUNT; i++) {
        Vrect_adc = getADCByChannel(&hadc1, 3);
        Vrect_adc_sum += Vrect_adc;
    }

    if (Vrect_adc_sum == 0) {
        Vrect_adc_raw_data = 0;
    } else {
        Vrect_adc_raw_data = Vrect_adc_sum / ADC_SAMPLE_COUNT;
    }
    // log_e("Vrect_adc_sum = %d,%d,%f\n",Vrect_adc_sum,Vrect_adc_raw_data,(float)Vrect_adc_raw_data * 3.3 / 4096);
}

uint16_t get_adc_vrect_raw_data(void)
{
    return Vrect_adc_raw_data;
}

bool get_vrect_status(void)
{
    if (get_adc_vrect_raw_data() > ADC_RAW_DATA) // 平均值大于0.7则认为RX处于充电状态
    {
        return TRUE;
    } else {
        return FALSE;
    }
}

uint32_t convert_vrect_time_to_seconds(uint32_t vrect_time) // vrect时间量纲转换为秒
{
    return vrect_time * MONITOR_OS_DELAY_TIME / 1000;
}

void calculate_vrect_maintain_time(void) // 计算VRECT维持时间，50ms为一个单位,可用于判断pass条件是否超时
{
    if (get_vrect_status() == TRUE) {
        vrect_maintain_time++;
    } else {
        vrect_maintain_time = 0;
    }
}

uint32_t get_vrect_maintain_time(void) // 获取VRECT维持时间 50ms为一个单位
{
    return vrect_maintain_time;
}

static void success_music(void)
{
    // 使用优化后的成功音效
    send_music(MUSIC_TYPE_PASS, 1);
}

void MB85RC16_initialize_exception_values(void)
{
    if (mb85rc16_sereen_S.pass_current > 1700 || mb85rc16_sereen_S.pass_current < 300) // 若电流大于1.7A 或者小于300mA
    {
        mb85rc16_sereen_S.pass_current = 800; // 800mA
        MB85RC16_Write_16bit_byte(PASS_CURRENT, mb85rc16_sereen_S.pass_current);
    }
    if (mb85rc16_sereen_S.pass_maxtime > (1000 * 60 * 3) || mb85rc16_sereen_S.pass_maxtime < (1000 * 10)) // 若时间超过3分钟 或者小于10秒
    {
        mb85rc16_sereen_S.pass_maxtime = (1000 * 60 * 1); // 1分钟
        MB85RC16_Write_32bit_byte(PASS_MAXTIME, mb85rc16_sereen_S.pass_maxtime);
    }
    if (mb85rc16_sereen_S.pass_percent > 100 || mb85rc16_sereen_S.pass_percent < 50) // 若百分比大于100 或者小于50
    {
        mb85rc16_sereen_S.pass_percent = 80; // 80%
        MB85RC16_Write_8bit_byte(PASS_PERCENT, mb85rc16_sereen_S.pass_percent);
    }

    if (mb85rc16_sereen_S.Select_Main_Type > DETAIL_MAIN) {
        mb85rc16_sereen_S.Select_Main_Type = STAND_MAIN;
        MB85RC16_Write_8bit_byte(SELECT_MAIN_TYPE, mb85rc16_sereen_S.Select_Main_Type);
    }

    if (mb85rc16_sereen_S.Compensate_Switch > COMPENSATE_OPERATION) {
        mb85rc16_sereen_S.Compensate_Switch = COMPENSATE_AUTO;
        MB85RC16_Write_8bit_byte(COMPENSATE_SWITCH, mb85rc16_sereen_S.Compensate_Switch);
    }

    if (mb85rc16_sereen_S.Compensate_Static_Current > 1000 || mb85rc16_sereen_S.Compensate_Static_Current < -1000) {
        mb85rc16_sereen_S.Compensate_Static_Current = 0;
        MB85RC16_Write_16bit_byte(COMPENSATE_STATIC_CURRENT, mb85rc16_sereen_S.Compensate_Static_Current);
    }
    if (mb85rc16_sereen_S.Compensate_Current > 1000 || mb85rc16_sereen_S.Compensate_Current < -1000) {
        mb85rc16_sereen_S.Compensate_Current = 0;
        MB85RC16_Write_16bit_byte(COMPENSATE_CURRENT, mb85rc16_sereen_S.Compensate_Current);
    }
    if (mb85rc16_sereen_S.Compensate_Voltage > 1000 || mb85rc16_sereen_S.Compensate_Voltage < -1000) {
        mb85rc16_sereen_S.Compensate_Voltage = 0;
        MB85RC16_Write_16bit_byte(COMPENSATE_VOLTAGE, mb85rc16_sereen_S.Compensate_Voltage);
    }
    if (mb85rc16_sereen_S.Compensate_Fre > 10 || mb85rc16_sereen_S.Compensate_Fre < -10) {
        mb85rc16_sereen_S.Compensate_Fre = 0;
        MB85RC16_Write_8bit_byte(COMPENSATE_FRE, mb85rc16_sereen_S.Compensate_Fre);
    }
    if (mb85rc16_sereen_S.Compensate_Temp > 10 || mb85rc16_sereen_S.Compensate_Temp < -10) {
        mb85rc16_sereen_S.Compensate_Temp = 0;
        MB85RC16_Write_8bit_byte(COMPENSATE_TEMP, mb85rc16_sereen_S.Compensate_Temp);
    }
}

static void Qi_pass_animate(void)
{
    static uint8_t pass_times = 0;
    // if ((get_isence_value() > (get_current_set_target() * mb85rc16_sereen_S.pass_percent / 100) && (get_isence_value() > 200 && get_isence_value() < 5000)) && get_vrect_status() == TRUE && Protocol_start == 0 && get_current_set_target() > 200) // 判断里面限制电流范围，是因为有时候拿放的时候电流会读错,判断get_current_set_target的值是因为有时候重新拿放，有时候出现电流值为800多，target为50（初始化）的情况
    if(get_isence_value() > (get_current_set_target() * mb85rc16_sereen_S.pass_percent / 100) && Protocol_start == 0 && get_current_set_target() > 200)
    {
        if((get_online_protocol_mode() == HUAWEI_MODE || mb85rc16_sereen_S.rx_mode == HUAWEI_TEST_MODE) && get_isence_value() < 1000)
        {
            return;
        }

        if((get_online_protocol_mode() == SS_PPDE_MODE || mb85rc16_sereen_S.rx_mode == SS_PPDE_TEST_MODE) && get_isence_value() < 1000)
        {
            return;
        }

        pass_times++;
        if (get_mode_value() > 0 && get_No_pop_up_windows() != TRUE && pass_times > 10) // 解决某些情况下弹AC_MISSING的错误状态
        {
            Protocol_start = 1;
            pass_times = 0;
            HAL_NVIC_DisableIRQ(EXTI15_10_IRQn);
            osDelay(200); // 防止界面切换导致卡机，确保界面不处于一个两个界面切换的状态
            success_music();
            if (xSemaphoreTake(lvgl_mutex, portMAX_DELAY) == pdTRUE) {
                ui_Screen_Qi_Test_set_screen_init();
                xSemaphoreGive(lvgl_mutex);
            }
        }
    }
    else
    {
        pass_times = 0; // 如果没有满足条件，则清零
    }

    if (get_vrect_status() == FALSE && get_isence_value() == 0) // 多重判断，避免错误读取以至于错误弹窗
    {
        Protocol_start = 0;
    }
}

static void APPLE_Window_Animate(void)
{
    static uint8_t mode_times = 0;
    if(get_mode_value() == RX_MODE_MPP_NEGO && get_Apple_Window_Flag() == APPLE_WINDOW_CLOSE)
    {
        mode_times++;
        if( mode_times > 1) // 滤波
        {
            mode_times = 0;
            Set_Apple_Window_Flag(APPLE_WINDOW_OPEN);
        }
    }
    else
    {
        mode_times = 0; // 如果没有满足条件，则清零
    }

    if (get_mode_value() == RX_MODE_MPP_NEGO && get_Apple_Window_Flag() == APPLE_WINDOW_OPEN && get_No_pop_up_windows() != TRUE) {
        if (xSemaphoreTake(lvgl_mutex, portMAX_DELAY) == pdTRUE) {
            Set_Apple_Window_Flag(APPLE_WINDOW_OPENING);
            HAL_NVIC_DisableIRQ(EXTI15_10_IRQn);
            ui_Screen_apple_window_init(); // MPP弹窗
            xSemaphoreGive(lvgl_mutex);
        }

    }
}

void Protocol_test_pass_animate(void)
{
    Qi_pass_animate(); // 拉载成功弹窗
    APPLE_Window_Animate(); // APPLE MPP弹窗
    nego_power_popup_check();     // 检测协商功率弹窗
}

// 插入usb后，设置最大的屏幕亮度
void usb_insert_set_max_screen_brightness(void)
{
    if (USB_IsConnnected() == TRUE && USB_condition.USB_insert_max_backlight == FALSE) {
        set_backlight_vaule(100);
        USB_condition.USB_insert_max_backlight = TRUE;
    }
}
// 插入USB后，RX断开，重置参数
void USB_insert_Rx_disconnect_reset_param(void)
{
    static uint8_t disconnect_times = 0;
    if (get_vrect_status() == FALSE) { // && get_first_load_done() == TRUE
        disconnect_times++;
        if (disconnect_times > 3) { // 断开超过3次，认为是真正断电
            disconnect_tx_load();
            clear_online_protocol_on_disconnect(); // 清除已建立的BPP协议标志
            disconnect_times = 0; // 清零
        }
    }
    else{
        disconnect_times = 0; // 如果没有满足条件，则清零
    }
}

void Normal_power_down_times(void)
{
    if (mb85rc16_sereen_S.normal_power_down_times > mb85rc16_sereen_S.max_power_down_times && mb85rc16_sereen_S.Auto_Qi_Detect_Switch != AUTO_DETECT_SWITCH_START) {
        MB85RC16_Write_8bit_byte(RX_MODE, NONE_TEST); // 超过20次，走BPP
    }
    if (mb85rc16_sereen_S.normal_power_down_times > 0) {
        mb85rc16_sereen_S.normal_power_down_times = 0;
        MB85RC16_Write_16bit_byte(NORMAL_POWER_DOWN_TIMES, 0);
    }
}

void USB_Condition_Control(void)
{
    USB_insert_Rx_disconnect_reset_param(); // 插入USB工况，RX断开，重置参数
    calculate_vrect_maintain_time();        // 计算VRECT维持时间
    USB_insert_rx_disconnect_times();       // USB插入RX断开次数
    adc_detect_vrect_value();               // 检测VRECT电压值
    usb_insert_set_max_screen_brightness(); // 插入usb后，设置最大的屏幕亮度
}


void monitor_task(void const *argument)
{
    osDelay(MONITOR_OS_DELAY_TIME);                      // 50ms
    PVD_config();                                        // 初始化掉电保护
    set_epp_nego_power();                                // 不插USB的情况，设置EPP的电压和neg功率 （插USB）
    Compatibility_selection();                           // 兼容模式选择
    MB85RC16_Write_16bit_byte(MAX_POWER_DOWN_TIMES, 15); // 设置无法进入APP的最大掉电次数
    fod_param_apply();
    nego_power_popup_init();                             // 初始化协商功率弹窗组件
    auto_detect_task_control_init();                     // 初始化自动检测任务控制
    for (;;) {
        get_button_set_buzzer();     // 按键声音响应
        monitor_power_down_buzzer(); // 掉电保护触发的声音
        process_stop_cep_request();  // 处理停止CEP请求
        set_pro_private();            // 设置pro挡位私有协议
        Ntc_temp_convert();           // NTC温度采样
        Protocol_test_pass_animate(); // 检测弹窗
        USB_Condition_Control();      // USB工况控制
        factory_data_reset();         // 恢复出厂设置
        Normal_power_down_times();    // 正常模式掉电过多，走BPP
        ask_send_process();           // ASK数据包轮询发送处理
        tx_response_read_process();   // TX响应数据包轮询读取处理
        Reset_auto_detect();
        auto_detect_task_control_process(); // 自动检测任务控制处理                       
        // detect_mode_change(); //检测拨码开关状态
        osDelay(MONITOR_OS_DELAY_TIME); // 50ms
    }
}

void set_stop_cep_flag(void)
{
    stop_cep_flag = 1;
}

void process_stop_cep_request(void)
{
    if (stop_cep_flag) {
        WP_set_stop_CEP();
        stop_cep_flag = 0;
    }
}



