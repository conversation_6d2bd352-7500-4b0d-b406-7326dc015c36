/**
 * @file mario_sounds_test.h
 * @brief 超级马里奥风格音效测试程序头文件
 * <AUTHOR> Assistant
 * @date 2025-07-30
 */

#ifndef MARIO_SOUNDS_TEST_H
#define MARIO_SOUNDS_TEST_H

#include "music_task.h"
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 马里奥音效类型定义
 */
typedef enum {
    MARIO_SOUND_JUMP = 1,           // 跳跃音效
    MARIO_SOUND_COIN,               // 金币音效
    MARIO_SOUND_STOMP,              // 踩怪物音效
    MARIO_SOUND_POWERUP,            // 获得蘑菇音效
    MARIO_SOUND_1UP,                // 1UP音效
    MARIO_SOUND_STAR,               // 获得星星音效
    MARIO_SOUND_PIPE,               // 进入管道音效
    MARIO_SOUND_CASTLE_COMPLETE,    // 城堡完成音效
    MARIO_SOUND_DEATH,              // 死亡音效
    MARIO_SOUND_THEME               // 主题音乐片段
} mario_sound_type_t;

/**
 * @brief 马里奥音效播放函数
 */

// 基本音效
extern void play_mario_jump(void);
extern void play_mario_stomp(void);
extern void play_mario_pipe(void);
extern void play_mario_death(void);

// 奖励音效
extern void play_mario_powerup(void);
extern void play_mario_star(void);
extern void play_mario_castle_complete(void);

// 音乐片段
extern void play_mario_theme_snippet(void);

/**
 * @brief 马里奥音效测试函数
 */

// 完整演示
extern void mario_sounds_demo(void);

// 交互式测试
extern void play_mario_sound_by_type(uint8_t sound_type);

// 系统测试
extern void test_mario_sound_system(void);

/**
 * @brief 使用说明
 * 
 * 1. 基本使用：
 *    - 调用 play_mario_jump() 播放跳跃音效
 *    - 调用 play_mario_coin_sound() 播放金币音效
 *    - 调用 play_mario_1up_sound() 播放1UP音效
 * 
 * 2. 完整演示：
 *    - 调用 mario_sounds_demo() 播放所有音效演示
 * 
 * 3. 交互式测试：
 *    - 调用 play_mario_sound_by_type(1-10) 播放指定类型音效
 * 
 * 4. 系统测试：
 *    - 调用 test_mario_sound_system() 进行完整系统测试
 * 
 * 音效特点：
 * - 使用高频音符，清脆悦耳
 * - 快节奏播放，欢快活泼
 * - 音符间有适当间隔，避免混音
 * - 音量动态变化，增强表现力
 * 
 * 推荐使用场景：
 * - 按键反馈：play_mario_jump()
 * - 成功提示：play_mario_coin_sound() 或 play_mario_1up_sound()
 * - 错误提示：play_mario_death()
 * - 完成任务：play_mario_castle_complete()
 * - 系统启动：play_mario_theme_snippet()
 */

#ifdef __cplusplus
}
#endif

#endif /* MARIO_SOUNDS_TEST_H */
