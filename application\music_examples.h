/**
 * @file music_examples.h
 * @brief 音乐系统使用示例和测试代码头文件
 * <AUTHOR> Assistant
 * @date 2025-07-30
 */

#ifndef MUSIC_EXAMPLES_H
#define MUSIC_EXAMPLES_H

#include "music_task.h"
#include "cmsis_os.h"
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 音乐播放接口函数
 */

// 系统音效
extern void play_startup_music(void);
extern void play_shutdown_music(void);
extern void play_calibration_music(void);

// 反馈音效
extern void play_success_sound(void);
extern void play_failure_sound(void);
extern void play_button_sound(void);

// 马里奥风格音效
extern void play_mario_coin_sound(void);
extern void play_mario_1up_sound(void);

// 警告音效
extern void play_emergency_sound(void);

/**
 * @brief 音乐测试函数
 */

// 功能测试
extern void test_all_music_effects(void);
extern void test_music_performance(void);
extern void test_music_quality(void);

// 系统测试
extern void music_system_init_test(void);
extern void test_buzzer_hardware(void);
extern void music_system_diagnosis(void);

/**
 * @brief 音乐控制函数
 */

// 状态查询
extern bool is_music_playing(void);

// 播放控制
extern void stop_current_music(void);

/**
 * @brief 自定义音乐示例
 */

// 预定义的自定义音乐
extern enhanced_music_t custom_startup_music;
extern enhanced_music_t custom_error_music;
extern enhanced_music_t custom_celebration_music;

/**
 * @brief 音乐使用建议
 * 
 * 1. 系统启动时调用 music_system_init_test() 测试音乐系统
 * 2. 使用 play_startup_music() 播放系统启动音乐
 * 3. 使用 play_button_sound() 播放按键反馈音
 * 4. 使用 play_success_sound() 和 play_failure_sound() 播放操作结果反馈
 * 5. 使用 play_emergency_sound() 播放紧急警告音
 * 6. 在需要时调用 stop_current_music() 停止音乐播放
 * 
 * 音乐系统特性：
 * - 支持平滑音符过渡，避免生硬的音调跳跃
 * - 支持多种音量包络（线性、指数、ADSR）
 * - 支持不同播放模式（普通、平滑、断奏、连奏）
 * - 支持可变节拍和动态时长
 * - 向后兼容原有的简单音乐播放接口
 */

#ifdef __cplusplus
}
#endif

#endif /* MUSIC_EXAMPLES_H */
