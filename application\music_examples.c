/**
 * @file music_examples.c
 * @brief 音乐系统使用示例和测试代码
 * <AUTHOR> Assistant
 * @date 2025-07-30
 */

#include "music_task.h"
#include "main.h"

/**
 * @brief 音乐系统使用示例
 * 
 * 这个文件展示了如何使用重构后的音乐系统，包括：
 * 1. 基本音乐播放
 * 2. 增强音乐播放（带平滑过渡）
 * 3. 自定义音乐创建
 * 4. 音乐效果测试
 */

// 示例：创建自定义音乐
enhanced_music_t custom_startup_music = {
    .note = {ALTO_DO, ALTO_MI, ALTO_SOL, HIGH_DO, HIGH_MI, HIGH_SOL, HIGH_DO},
    .volume = {L_NOISE, M_NOISE, M_NOISE, H_NOISE, H_NOISE, MAX_NOISE, H_NOISE},
    .duration = {150, 150, 150, 200, 200, 300, 400},
    .transition = {0, 1, 1, 1, 1, 1, 0},
    .length = 7,
    .mode = MUSIC_MODE_SMOOTH,
    .envelope = ENVELOPE_ADSR,
    .tempo = 120
};

enhanced_music_t custom_error_music = {
    .note = {HIGH_DO, ALTO_SOL, ALTO_MI, ALTO_DO, BASS_SOL},
    .volume = {MAX_NOISE, H_NOISE, M_NOISE, M_NOISE, L_NOISE},
    .duration = {100, 100, 150, 150, 300},
    .transition = {0, 1, 1, 1, 0},
    .length = 5,
    .mode = MUSIC_MODE_SMOOTH,
    .envelope = ENVELOPE_EXPONENTIAL,
    .tempo = 80
};

enhanced_music_t custom_celebration_music = {
    .note = {ALTO_DO, ALTO_RE, ALTO_MI, ALTO_FA, ALTO_SOL, ALTO_LA, ALTO_SI, HIGH_DO, 
             HIGH_RE, HIGH_DO, ALTO_SI, ALTO_LA, ALTO_SOL, ALTO_FA, ALTO_MI, ALTO_DO},
    .volume = {M_NOISE, M_NOISE, M_NOISE, M_NOISE, H_NOISE, H_NOISE, H_NOISE, MAX_NOISE,
               MAX_NOISE, H_NOISE, H_NOISE, M_NOISE, M_NOISE, M_NOISE, M_NOISE, L_NOISE},
    .duration = {120, 120, 120, 120, 150, 150, 150, 200,
                 200, 150, 150, 120, 120, 120, 120, 300},
    .transition = {0, 1, 1, 1, 1, 1, 1, 1,
                   1, 1, 1, 1, 1, 1, 1, 0},
    .length = 16,
    .mode = MUSIC_MODE_LEGATO,
    .envelope = ENVELOPE_LINEAR,
    .tempo = 140
};

/**
 * @brief 播放系统启动音乐
 */
void play_startup_music(void)
{
    // 使用增强音乐系统播放启动音乐
    send_enhanced_music(MUSIC_ARMED, 1);
}

/**
 * @brief 播放系统关闭音乐
 */
void play_shutdown_music(void)
{
    send_enhanced_music(MUSIC_DISARMED, 1);
}

/**
 * @brief 播放校准音乐
 */
void play_calibration_music(void)
{
    send_enhanced_music(MUSIC_IMUCALI, 1);
}

/**
 * @brief 播放成功提示音
 */
void play_success_sound(void)
{
    send_music(MUSIC_TYPE_SUCCESS, 1);
}

/**
 * @brief 播放失败提示音
 */
void play_failure_sound(void)
{
    send_music(MUSIC_TYPE_FAIL, 1);
}

/**
 * @brief 播放按键音效
 */
void play_button_sound(void)
{
    send_music(MUSCI_TYPE_BUTTON, 1);
}

/**
 * @brief 播放紧急警告音
 */
void play_emergency_sound(void)
{
    send_enhanced_music(MUSIC_RADIO_LOSS, 1);
}

/**
 * @brief 测试所有音乐效果
 * 
 * 这个函数依次播放所有音乐效果，用于测试音乐系统
 */
void test_all_music_effects(void)
{
    // 测试增强音乐
    play_startup_music();
    osDelay(2000);
    
    play_calibration_music();
    osDelay(3000);
    
    play_shutdown_music();
    osDelay(2000);
    
    // 测试传统音乐
    play_button_sound();
    osDelay(1000);
    
    play_success_sound();
    osDelay(2000);
    
    play_failure_sound();
    osDelay(2000);
    
    // 测试紧急音效
    play_emergency_sound();
    osDelay(3000);
}

/**
 * @brief 音乐系统性能测试
 * 
 * 测试音乐系统的响应时间和资源使用情况
 */
void test_music_performance(void)
{
    uint32_t start_time, end_time;
    
    // 测试音乐启动时间
    start_time = HAL_GetTick();
    play_startup_music();
    end_time = HAL_GetTick();
    
    // 可以通过串口输出测试结果
    // printf("Music start time: %lu ms\n", end_time - start_time);
    
    // 测试音乐切换时间
    osDelay(1000);
    start_time = HAL_GetTick();
    play_success_sound();
    end_time = HAL_GetTick();
    
    // printf("Music switch time: %lu ms\n", end_time - start_time);
}

/**
 * @brief 音乐质量测试
 * 
 * 测试不同音乐模式的效果
 */
void test_music_quality(void)
{
    // 测试平滑过渡模式
    send_enhanced_music(MUSIC_ARMED, 1);
    osDelay(3000);
    
    // 测试断奏模式
    send_enhanced_music(MUSIC_RADIO_LOSS, 1);
    osDelay(3000);
    
    // 测试连奏模式
    send_enhanced_music(MUSIC_IMUCALI, 1);
    osDelay(4000);
}

/**
 * @brief 获取当前音乐播放状态
 * 
 * @return true 正在播放音乐
 * @return false 没有播放音乐
 */
bool is_music_playing(void)
{
    // 这里需要访问音乐系统的内部状态
    // 在实际实现中，可能需要在music_task.c中添加相应的接口函数
    return false; // 占位符
}

/**
 * @brief 停止当前播放的音乐
 */
void stop_current_music(void)
{
    send_music(QUIET, 1);
}

/**
 * @brief 音乐系统初始化测试
 *
 * 在系统启动时调用，测试音乐系统是否正常工作
 */
void music_system_init_test(void)
{
    // 播放简短的测试音
    play_button_sound();
    osDelay(500);

    // 播放启动音乐
    play_startup_music();
}

/**
 * @brief 蜂鸣器硬件测试
 */
void test_buzzer_hardware(void)
{
    extern TIM_HandleTypeDef htim8;

    // 测试1: 直接PWM输出测试
    __HAL_TIM_SET_PRESCALER(&htim8, 1000);  // 设置一个中等频率
    __HAL_TIM_SET_COMPARE(&htim8, TIM_CHANNEL_1, 50); // 50%占空比
    osDelay(1000);
    __HAL_TIM_SET_COMPARE(&htim8, TIM_CHANNEL_1, 0);  // 关闭

    osDelay(500);

    // 测试2: 不同频率测试
    uint16_t test_prescalers[] = {500, 1000, 2000, 3000, 4000};
    for (int i = 0; i < 5; i++) {
        __HAL_TIM_SET_PRESCALER(&htim8, test_prescalers[i]);
        __HAL_TIM_SET_COMPARE(&htim8, TIM_CHANNEL_1, 60);
        osDelay(400);
        __HAL_TIM_SET_COMPARE(&htim8, TIM_CHANNEL_1, 0);
        osDelay(200);
    }
}

/**
 * @brief 音乐系统完整诊断
 */
void music_system_diagnosis(void)
{
    // 1. 硬件测试
    test_buzzer_hardware();
    osDelay(1000);

    // 2. 基本音符测试
    test_buzzer_basic();
    osDelay(1000);

    // 3. 音量测试
    test_buzzer_volume();
    osDelay(1000);

    // 4. 音乐播放测试
    play_startup_music();
    osDelay(3000);

    play_success_sound();
    osDelay(2000);
}
