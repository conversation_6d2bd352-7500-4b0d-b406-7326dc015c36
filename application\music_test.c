/**
 * @file music_test.c
 * @brief 音乐系统测试程序
 * <AUTHOR> Assistant
 * @date 2025-07-30
 */

#include "music_task.h"
#include "music_examples.h"
#include "cmsis_os.h"
#include "main.h"

/**
 * @brief 音乐系统基本功能测试
 */
void basic_music_test(void)
{
    // 测试1: 播放启动音乐
    play_startup_music();
    osDelay(3000);
    
    // 测试2: 播放按键音效
    play_button_sound();
    osDelay(1000);
    
    // 测试3: 播放成功音效
    play_success_sound();
    osDelay(2000);
    
    // 测试4: 播放失败音效
    play_failure_sound();
    osDelay(2000);
    
    // 测试5: 播放关闭音乐
    play_shutdown_music();
    osDelay(2000);
}

/**
 * @brief 增强音乐功能测试
 */
void enhanced_music_test(void)
{
    // 测试平滑过渡效果
    send_enhanced_music(MUSIC_ARMED, 1);
    osDelay(3000);
    
    // 测试校准音乐
    send_enhanced_music(MUSIC_IMUCALI, 1);
    osDelay(4000);
    
    // 测试参数保存音乐
    send_enhanced_music(MUSIC_PARAMCALI, 1);
    osDelay(3000);
    
    // 测试紧急警告音
    send_enhanced_music(MUSIC_RADIO_LOSS, 1);
    osDelay(3000);
}

/**
 * @brief 音乐过渡效果测试
 */
void transition_test(void)
{
    // 测试不同音符间的平滑过渡
    uint16_t test_notes[] = {ALTO_DO, ALTO_MI, ALTO_SOL, HIGH_DO, ALTO_DO};
    uint16_t test_volumes[] = {L_NOISE, M_NOISE, H_NOISE, MAX_NOISE, L_NOISE};
    
    for (int i = 0; i < 4; i++) {
        smooth_note_transition(test_notes[i], test_notes[i+1], 
                              test_volumes[i], test_volumes[i+1], 
                              MUSIC_TRANSITION_STEPS);
        
        // 模拟过渡过程
        for (int step = 0; step < MUSIC_TRANSITION_STEPS; step++) {
            update_transition();
            osDelay(50);
        }
        osDelay(200);
    }
}

/**
 * @brief 音量包络测试
 */
void envelope_test(void)
{
    uint16_t base_volume = H_NOISE;
    uint16_t duration = 1000; // 1秒
    
    // 测试线性包络
    for (uint16_t t = 0; t < duration; t += 50) {
        uint16_t volume = calculate_envelope(base_volume, ENVELOPE_LINEAR, t, duration);
        beepUpdate(volume, BeepCode[ALTO_DO]);
        osDelay(50);
    }
    
    osDelay(500);
    
    // 测试指数包络
    for (uint16_t t = 0; t < duration; t += 50) {
        uint16_t volume = calculate_envelope(base_volume, ENVELOPE_EXPONENTIAL, t, duration);
        beepUpdate(volume, BeepCode[ALTO_MI]);
        osDelay(50);
    }
    
    osDelay(500);
    
    // 测试ADSR包络
    for (uint16_t t = 0; t < duration; t += 50) {
        uint16_t volume = calculate_envelope(base_volume, ENVELOPE_ADSR, t, duration);
        beepUpdate(volume, BeepCode[ALTO_SOL]);
        osDelay(50);
    }
    
    beepUpdate(0, 0); // 停止声音
}

/**
 * @brief 性能压力测试
 */
void performance_stress_test(void)
{
    uint32_t start_time = HAL_GetTick();
    
    // 快速切换音乐测试
    for (int i = 0; i < 10; i++) {
        play_button_sound();
        osDelay(100);
        play_success_sound();
        osDelay(100);
        stop_current_music();
        osDelay(50);
    }
    
    uint32_t end_time = HAL_GetTick();
    uint32_t elapsed = end_time - start_time;
    
    // 可以通过调试接口输出测试结果
    // printf("Performance test completed in %lu ms\n", elapsed);
}

/**
 * @brief 音乐系统完整测试套件
 */
void complete_music_test_suite(void)
{
    // 初始化测试
    music_system_init_test();
    osDelay(2000);
    
    // 基本功能测试
    basic_music_test();
    osDelay(1000);
    
    // 增强功能测试
    enhanced_music_test();
    osDelay(1000);
    
    // 过渡效果测试
    transition_test();
    osDelay(1000);
    
    // 音量包络测试
    envelope_test();
    osDelay(1000);
    
    // 性能测试
    performance_stress_test();
    osDelay(1000);
    
    // 测试完成提示
    play_success_sound();
}

/**
 * @brief 交互式音乐测试
 * 
 * 可以通过按键或其他输入方式触发不同的音乐测试
 */
void interactive_music_test(uint8_t test_type)
{
    switch (test_type) {
        case 1:
            basic_music_test();
            break;
        case 2:
            enhanced_music_test();
            break;
        case 3:
            transition_test();
            break;
        case 4:
            envelope_test();
            break;
        case 5:
            performance_stress_test();
            break;
        case 0:
        default:
            complete_music_test_suite();
            break;
    }
}

/**
 * @brief 音乐系统健康检查
 * 
 * @return true 音乐系统正常
 * @return false 音乐系统异常
 */
bool music_system_health_check(void)
{
    // 测试基本播放功能
    play_button_sound();
    osDelay(500);
    
    // 测试增强音乐功能
    send_enhanced_music(MUSIC_ARMED, 1);
    osDelay(1000);
    stop_current_music();
    
    // 测试过渡功能
    smooth_note_transition(ALTO_DO, ALTO_MI, L_NOISE, H_NOISE, 4);
    for (int i = 0; i < 4; i++) {
        if (!update_transition()) break;
        osDelay(25);
    }
    
    // 如果所有测试都通过，返回true
    return true;
}

/**
 * @brief 音乐系统调试信息输出
 */
void music_system_debug_info(void)
{
    // 输出系统配置信息
    // printf("Music System Configuration:\n");
    // printf("- Max Length: %d\n", MUSIC_MAX_LENGHT);
    // printf("- Transition Steps: %d\n", MUSIC_TRANSITION_STEPS);
    // printf("- Min Delay: %d ms\n", MUSIC_MIN_DELAY);
    // printf("- Max Delay: %d ms\n", MUSIC_MAX_DELAY);
    
    // 输出当前状态
    // printf("Current State:\n");
    // printf("- Playing: %s\n", is_music_playing() ? "Yes" : "No");
}
