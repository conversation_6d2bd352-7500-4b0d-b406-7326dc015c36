/**
 * @file mario_sounds_test.c
 * @brief 超级马里奥风格音效测试程序
 * <AUTHOR> Assistant
 * @date 2025-07-30
 */

#include "music_task.h"
#include "music_examples.h"
#include "cmsis_os.h"
#include "main.h"

/**
 * @brief 播放经典的超级马里奥主题音乐片段
 */
void play_mario_theme_snippet(void)
{
    // 超级马里奥主题音乐的开头几个音符
    // "滴滴滴 滴滴滴滴 滴滴滴 滴滴滴滴"
    
    // 第一小节：E-E-E C-E G-G
    beepUpdate(H_NOISE, BeepCode[HIGH_MI]);
    osDelay(120);
    beepUpdate(0, 0);
    osDelay(40);
    
    beepUpdate(H_NOISE, BeepCode[HIGH_MI]);
    osDelay(120);
    beepUpdate(0, 0);
    osDelay(40);
    
    beepUpdate(H_NOISE, BeepCode[HIGH_MI]);
    osDelay(120);
    beepUpdate(0, 0);
    osDelay(80);
    
    beepUpdate(M_NOISE, BeepCode[HIGH_DO]);
    osDelay(120);
    beepUpdate(0, 0);
    osDelay(40);
    
    beepUpdate(H_NOISE, BeepCode[HIGH_MI]);
    osDelay(120);
    beepUpdate(0, 0);
    osDelay(80);
    
    beepUpdate(MAX_NOISE, BeepCode[HIGH_SOL]);
    osDelay(200);
    beepUpdate(0, 0);
    osDelay(200);
    
    beepUpdate(M_NOISE, BeepCode[ALTO_SOL]);
    osDelay(200);
    beepUpdate(0, 0);
    osDelay(300);
}

/**
 * @brief 播放马里奥跳跃音效
 */
void play_mario_jump(void)
{
    beepUpdate(H_NOISE, BeepCode[HIGH_DO]);
    osDelay(50);
    beepUpdate(MAX_NOISE, BeepCode[HIGH_MI]);
    osDelay(80);
    beepUpdate(MAX_NOISE, BeepCode[HIGH_SOL]);
    osDelay(100);
    beepUpdate(0, 0);
}

/**
 * @brief 播放马里奥踩怪物音效
 */
void play_mario_stomp(void)
{
    beepUpdate(MAX_NOISE, BeepCode[ALTO_DO]);
    osDelay(60);
    beepUpdate(H_NOISE, BeepCode[BASS_SOL]);
    osDelay(80);
    beepUpdate(0, 0);
}

/**
 * @brief 播放马里奥获得蘑菇音效
 */
void play_mario_powerup(void)
{
    uint16_t powerup_notes[] = {
        ALTO_SOL, ALTO_LA, ALTO_SI, HIGH_DO, HIGH_RE, HIGH_MI, HIGH_FA, HIGH_SOL,
        HIGH_LA, HIGH_SI, SUPER_HIGH_DO, SUPER_HIGH_RE, SUPER_HIGH_MI, SUPER_HIGH_FA, SUPER_HIGH_SOL
    };
    
    for (int i = 0; i < 15; i++) {
        beepUpdate(MAX_NOISE, BeepCode[powerup_notes[i]]);
        osDelay(60);
        beepUpdate(0, 0);
        osDelay(20);
    }
}

/**
 * @brief 播放马里奥死亡音效
 */
void play_mario_death(void)
{
    // 下行的悲伤音调
    uint16_t death_notes[] = {HIGH_DO, ALTO_SI, ALTO_LA, ALTO_SOL, ALTO_FA, ALTO_MI, ALTO_RE, ALTO_DO};
    uint16_t durations[] = {150, 150, 150, 150, 200, 200, 250, 400};
    
    for (int i = 0; i < 8; i++) {
        beepUpdate(H_NOISE - (i * 8), BeepCode[death_notes[i]]);
        osDelay(durations[i]);
        beepUpdate(0, 0);
        osDelay(50);
    }
}

/**
 * @brief 播放马里奥进入管道音效
 */
void play_mario_pipe(void)
{
    // 下行滑音效果
    for (int freq = HIGH_SOL; freq >= ALTO_DO; freq--) {
        if (freq <= SUPER_HIGH_SI) {  // 确保不超出数组范围
            beepUpdate(H_NOISE, BeepCode[freq]);
            osDelay(30);
        }
    }
    beepUpdate(0, 0);
}

/**
 * @brief 播放马里奥获得星星音效
 */
void play_mario_star(void)
{
    // 快速的上下音阶循环
    uint16_t star_notes[] = {HIGH_DO, HIGH_MI, HIGH_SOL, SUPER_HIGH_DO, HIGH_SOL, HIGH_MI};
    
    for (int cycle = 0; cycle < 3; cycle++) {
        for (int i = 0; i < 6; i++) {
            beepUpdate(MAX_NOISE, BeepCode[star_notes[i]]);
            osDelay(80);
            beepUpdate(0, 0);
            osDelay(20);
        }
    }
}

/**
 * @brief 播放马里奥城堡完成音效
 */
void play_mario_castle_complete(void)
{
    // 胜利的上行音阶
    uint16_t victory_notes[] = {
        ALTO_DO, ALTO_RE, ALTO_MI, ALTO_FA, ALTO_SOL, ALTO_LA, ALTO_SI,
        HIGH_DO, HIGH_RE, HIGH_MI, HIGH_FA, HIGH_SOL, HIGH_LA, HIGH_SI, SUPER_HIGH_DO
    };
    
    for (int i = 0; i < 15; i++) {
        beepUpdate(MAX_NOISE, BeepCode[victory_notes[i]]);
        osDelay(100);
        beepUpdate(0, 0);
        osDelay(30);
    }
    
    // 最后的长音
    beepUpdate(MAX_NOISE, BeepCode[SUPER_HIGH_DO]);
    osDelay(500);
    beepUpdate(0, 0);
}

/**
 * @brief 完整的马里奥音效演示
 */
void mario_sounds_demo(void)
{
    // 1. 主题音乐片段
    play_mario_theme_snippet();
    osDelay(1000);
    
    // 2. 跳跃音效
    for (int i = 0; i < 3; i++) {
        play_mario_jump();
        osDelay(300);
    }
    osDelay(500);
    
    // 3. 金币音效
    for (int i = 0; i < 5; i++) {
        play_mario_coin_sound();
        osDelay(200);
    }
    osDelay(500);
    
    // 4. 踩怪物音效
    for (int i = 0; i < 3; i++) {
        play_mario_stomp();
        osDelay(300);
    }
    osDelay(500);
    
    // 5. 获得蘑菇音效
    play_mario_powerup();
    osDelay(1000);
    
    // 6. 1UP音效
    play_mario_1up_sound();
    osDelay(1000);
    
    // 7. 获得星星音效
    play_mario_star();
    osDelay(1000);
    
    // 8. 进入管道音效
    play_mario_pipe();
    osDelay(1000);
    
    // 9. 城堡完成音效
    play_mario_castle_complete();
    osDelay(1000);
    
    // 10. 死亡音效（最后播放）
    play_mario_death();
}

/**
 * @brief 交互式马里奥音效测试
 * 
 * @param sound_type 音效类型 (1-10)
 */
void play_mario_sound_by_type(uint8_t sound_type)
{
    switch (sound_type) {
        case 1:
            play_mario_jump();
            break;
        case 2:
            play_mario_coin_sound();
            break;
        case 3:
            play_mario_stomp();
            break;
        case 4:
            play_mario_powerup();
            break;
        case 5:
            play_mario_1up_sound();
            break;
        case 6:
            play_mario_star();
            break;
        case 7:
            play_mario_pipe();
            break;
        case 8:
            play_mario_castle_complete();
            break;
        case 9:
            play_mario_death();
            break;
        case 10:
            play_mario_theme_snippet();
            break;
        default:
            mario_sounds_demo();
            break;
    }
}

/**
 * @brief 马里奥音效系统测试
 */
void test_mario_sound_system(void)
{
    // 播放完整演示
    mario_sounds_demo();
}
