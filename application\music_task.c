
#include "music_task.h"
#include "test_task.h"
#include "data_task.h"
#include "main.h"
#include "cmsis_os.h"
#include <time.h>
#include "tim.h"
#include "struct_typedef.h"
#include "usart.h"
#include "elog.h"

#include <stdio.h>
#include <stdarg.h>
#include "string.h"


extern TIM_HandleTypeDef htim2;

volatile uint16_t commandState;
uint16_t test_value, ref;

typedef struct {
    uint16_t note[MUSIC_MAX_LENGHT];
    uint16_t volume[MUSIC_MAX_LENGHT];
    uint16_t lenght;
} BeepSound_t;

// 音乐过渡状态
typedef struct {
    uint16_t current_note;
    uint16_t target_note;
    uint16_t current_volume;
    uint16_t target_volume;
    uint16_t transition_step;
    uint16_t total_steps;
    bool is_transitioning;
} music_transition_t;

static music_transition_t music_state = {0};

// 标准音乐频率表 (Hz) - 基于A4=440Hz的十二平均律
const float note_frequencies[] = {
    // 低音 (C3-B3)
    130.81, 146.83, 164.81, 174.61, 196.00, 220.00, 246.94,
    // 中音 (C4-B4)
    261.63, 293.66, 329.63, 349.23, 392.00, 440.00, 493.88,
    // 高音 (C5-B5)
    523.25, 587.33, 659.25, 698.46, 783.99, 880.00, 987.77
};

// 根据TIM8配置计算预分频值
// TIM8: 96MHz时钟, Period=99
// 目标频率 = 96MHz / (Prescaler × 100)
// Prescaler = 96MHz / (目标频率 × 100)
int32_t BeepCode[] = {
    // 低音 (C3-B3) - 计算得出的预分频值
    7340, 6537, 5825, 5498, 4898, 4364, 3888,
    // 中音 (C4-B4)
    3670, 3268, 2912, 2749, 2449, 2182, 1944,
    // 高音 (C5-B5)
    1835, 1634, 1456, 1375, 1225, 1091, 972
};
uint16_t test_sound = 700;
const uint16_t CHARGE_NOTE = 700;

// 优化后的音乐数据 - 参考经典音效设计，使用更优美的旋律
enhanced_music_t enhancedMusic[] = {
    // MUSIC_ARMED - 解锁提示音：经典的"叮咚"上行音效，类似Windows登录音
    {
        .note = {ALTO_DO, ALTO_SOL, HIGH_DO},
        .volume = {M_NOISE, H_NOISE, MAX_NOISE},
        .duration = {150, 150, 300},
        .transition = {0, 1, 1},
        .length = 3,
        .mode = MUSIC_MODE_SMOOTH,
        .envelope = ENVELOPE_LINEAR,
        .tempo = 120
    },

    // MUSIC_DISARMED - 上锁提示音：温和的下行三和弦，类似iPhone锁屏音
    {
        .note = {HIGH_DO, ALTO_LA, ALTO_FA, ALTO_DO},
        .volume = {H_NOISE, M_NOISE, M_NOISE, L_NOISE},
        .duration = {120, 120, 150, 250},
        .transition = {0, 1, 1, 1},
        .length = 4,
        .mode = MUSIC_MODE_SMOOTH,
        .envelope = ENVELOPE_EXPONENTIAL,
        .tempo = 100
    },

    // MUSIC_IMUCALI - 校准提示音：循环的五音音阶，表示进行中的过程
    {
        .note = {ALTO_DO, ALTO_RE, ALTO_MI, ALTO_SOL, ALTO_LA, ALTO_SOL, ALTO_MI, ALTO_RE},
        .volume = {M_NOISE, M_NOISE, H_NOISE, H_NOISE, MAX_NOISE, H_NOISE, M_NOISE, M_NOISE},
        .duration = {120, 120, 120, 120, 180, 120, 120, 120},
        .transition = {0, 1, 1, 1, 1, 1, 1, 1},
        .length = 8,
        .mode = MUSIC_MODE_LEGATO,
        .envelope = ENVELOPE_LINEAR,
        .tempo = 140
    },

    // MUSIC_PARAMCALI - 参数保存：经典的"保存成功"和弦进行
    {
        .note = {ALTO_DO, ALTO_MI, ALTO_SOL, HIGH_DO, HIGH_MI, HIGH_DO},
        .volume = {M_NOISE, H_NOISE, H_NOISE, MAX_NOISE, MAX_NOISE, H_NOISE},
        .duration = {180, 180, 180, 250, 200, 350},
        .transition = {0, 1, 1, 1, 1, 1},
        .length = 6,
        .mode = MUSIC_MODE_SMOOTH,
        .envelope = ENVELOPE_ADSR,
        .tempo = 90
    },

    // MUSIC_MAGCALI - MAG校准：优美的波浪式音阶，类似水滴声效
    {
        .note = {ALTO_DO, ALTO_MI, ALTO_SOL, ALTO_MI, ALTO_DO, ALTO_FA, ALTO_LA, HIGH_DO, ALTO_LA, ALTO_FA},
        .volume = {L_NOISE, M_NOISE, H_NOISE, M_NOISE, L_NOISE, M_NOISE, H_NOISE, MAX_NOISE, H_NOISE, M_NOISE},
        .duration = {100, 100, 150, 100, 100, 100, 150, 200, 150, 150},
        .transition = {0, 1, 1, 1, 1, 1, 1, 1, 1, 1},
        .length = 10,
        .mode = MUSIC_MODE_SMOOTH,
        .envelope = ENVELOPE_LINEAR,
        .tempo = 130
    },

    // MUSIC_RADIO_LOSS - 失控警告：紧急警报音，类似汽车报警器
    {
        .note = {HIGH_DO, ALTO_DO, HIGH_DO, ALTO_DO, HIGH_DO, ALTO_DO, HIGH_DO, ALTO_DO},
        .volume = {MAX_NOISE, H_NOISE, MAX_NOISE, H_NOISE, MAX_NOISE, H_NOISE, MAX_NOISE, H_NOISE},
        .duration = {120, 120, 120, 120, 120, 120, 120, 120},
        .transition = {0, 0, 0, 0, 0, 0, 0, 0},
        .length = 8,
        .mode = MUSIC_MODE_STACCATO,
        .envelope = ENVELOPE_NONE,
        .tempo = 180
    }
};

// 保持原有的简单音乐结构以兼容现有代码 - 重新设计更好听的音效
BeepSound_t beepNewSound[] = {
    // 按键按下 - 清脆的"叮"声，类似iPhone按键音
    {
        {HIGH_DO, HIGH_MI},
        {H_NOISE, N_NOISE},
        2
    },

    // 测试成功 - 经典的成功音效，类似超级马里奥金币音
    {
        {ALTO_MI, ALTO_SOL, HIGH_DO, HIGH_MI, HIGH_SOL},
        {M_NOISE, H_NOISE, H_NOISE, MAX_NOISE, N_NOISE},
        5
    },

    // 测试失败 - 低沉的警告音，类似错误提示音
    {
        {ALTO_RE, ALTO_RE, BASS_LA, BASS_LA, BASS_SOL},
        {H_NOISE, H_NOISE, H_NOISE, H_NOISE, N_NOISE},
        5
    },
};

void buzzer_on1(uint16_t psc, uint16_t pwm)
{
    // 设置预分频器以改变频率
    __HAL_TIM_SET_PRESCALER(&htim8, psc);
    // 设置PWM占空比以改变音量 (pwm值应该是0-99之间，因为Period=99)
    __HAL_TIM_SET_COMPARE(&htim8, TIM_CHANNEL_1, pwm);
}

void beepUpdate(uint16_t beepSound, uint16_t note)
{
#if (BUZZER_ENABLE == 1)
    // beepSound: 音量 (0-100)
    // note: 预分频值 (用于设置频率)

    // 限制音量范围 (0-85，对应PWM占空比0%-85%)
    uint16_t volume = constrainInt(beepSound, BEEP_MIN_NOISE, BEEP_MAX_NOISE);
    // 限制预分频值范围
    uint16_t prescaler = constrainInt(note, BEEP_NOTE_MIN, BEEP_NOTE_MAX);

    // 如果音量为0，直接关闭蜂鸣器
    if (volume == 0) {
        __HAL_TIM_SET_COMPARE(&htim8, TIM_CHANNEL_1, 0);
        return;
    }

    // 设置频率和音量
    buzzer_on1(prescaler, volume);
#endif
}

// 线性插值函数，用于平滑过渡
uint16_t lerp(uint16_t start, uint16_t end, uint16_t step, uint16_t total_steps)
{
    if (total_steps == 0) return end;
    return start + ((end - start) * step) / total_steps;
}

// 音量包络计算
uint16_t calculate_envelope(uint16_t base_volume, envelope_type_t envelope, uint16_t time_ms, uint16_t duration_ms)
{
    if (envelope == ENVELOPE_NONE) return base_volume;

    float progress = (float)time_ms / duration_ms;
    if (progress > 1.0f) progress = 1.0f;

    switch (envelope) {
        case ENVELOPE_LINEAR:
            // 线性淡入淡出
            if (progress < 0.1f) {
                return (uint16_t)(base_volume * (progress / 0.1f));
            } else if (progress > 0.9f) {
                return (uint16_t)(base_volume * ((1.0f - progress) / 0.1f));
            }
            return base_volume;

        case ENVELOPE_EXPONENTIAL:
            // 指数衰减
            return (uint16_t)(base_volume * (1.0f - progress * progress));

        case ENVELOPE_ADSR:
            // 简化的ADSR包络
            if (progress < 0.1f) {
                // Attack
                return (uint16_t)(base_volume * (progress / 0.1f));
            } else if (progress < 0.3f) {
                // Decay
                float decay_progress = (progress - 0.1f) / 0.2f;
                return (uint16_t)(base_volume * (1.0f - 0.2f * decay_progress));
            } else if (progress < 0.8f) {
                // Sustain
                return (uint16_t)(base_volume * 0.8f);
            } else {
                // Release
                float release_progress = (progress - 0.8f) / 0.2f;
                return (uint16_t)(base_volume * 0.8f * (1.0f - release_progress));
            }

        default:
            return base_volume;
    }
}

// 平滑音符过渡
void smooth_note_transition(uint16_t from_note, uint16_t to_note, uint16_t from_volume, uint16_t to_volume, uint16_t steps)
{
    music_state.current_note = from_note;
    music_state.target_note = to_note;
    music_state.current_volume = from_volume;
    music_state.target_volume = to_volume;
    music_state.transition_step = 0;
    music_state.total_steps = steps;
    music_state.is_transitioning = (steps > 0 && from_note != to_note);
}

// 更新过渡状态
bool update_transition(void)
{
    if (!music_state.is_transitioning) return false;

    music_state.transition_step++;

    if (music_state.transition_step >= music_state.total_steps) {
        music_state.current_note = music_state.target_note;
        music_state.current_volume = music_state.target_volume;
        music_state.is_transitioning = false;
        return false;
    }

    // 计算当前插值 - 直接对频率值进行插值
    uint16_t from_freq = BeepCode[music_state.current_note];
    uint16_t to_freq = BeepCode[music_state.target_note];
    uint16_t current_freq = lerp(from_freq, to_freq,
                                music_state.transition_step,
                                music_state.total_steps);
    music_state.current_note = current_freq; // 存储频率值而不是索引

    music_state.current_volume = lerp(music_state.current_volume,
                                     music_state.target_volume,
                                     music_state.transition_step,
                                     music_state.total_steps);

    return true;
}

BeepSound_t frequencyCode;
void prepareBeepPacket(BeepSound_t *sound)
{
    uint16_t lenght = sound->lenght;
    for (uint16_t i = 0; i < lenght; i++) {
        frequencyCode.note[i]   = BeepCode[sound->note[i]];
        frequencyCode.volume[i] = sound->volume[i];
    }
}

// 增强音乐播放状态
typedef struct {
    enhanced_music_t *current_music;
    uint16_t note_index;
    uint32_t note_start_time;
    uint32_t current_time;
    bool is_playing;
    bool use_enhanced_mode;
} enhanced_music_state_t;

static enhanced_music_state_t enhanced_state = {0};

// 播放增强音乐
void play_enhanced_music(uint8_t music_index)
{
    if (music_index == 0 || music_index > (sizeof(enhancedMusic) / sizeof(enhancedMusic[0]))) {
        enhanced_state.is_playing = false;
        return;
    }

    enhanced_state.current_music = &enhancedMusic[music_index - 1];
    enhanced_state.note_index = 0;
    enhanced_state.note_start_time = HAL_GetTick();
    enhanced_state.current_time = enhanced_state.note_start_time;
    enhanced_state.is_playing = true;
    enhanced_state.use_enhanced_mode = true;

    // 初始化第一个音符
    if (enhanced_state.current_music->length > 0) {
        uint16_t first_note = enhanced_state.current_music->note[0];
        uint16_t first_volume = enhanced_state.current_music->volume[0];

        beepUpdate(first_volume, BeepCode[first_note]);
        music_state.current_note = first_note;
        music_state.current_volume = first_volume;
        music_state.is_transitioning = false;
    }
}

// 更新增强音乐播放
bool_t update_enhanced_music(void)
{
    if (!enhanced_state.is_playing || !enhanced_state.current_music) {
        return false;
    }

    enhanced_state.current_time = HAL_GetTick();
    uint32_t elapsed = enhanced_state.current_time - enhanced_state.note_start_time;

    enhanced_music_t *music = enhanced_state.current_music;
    uint16_t current_idx = enhanced_state.note_index;

    // 检查是否需要切换到下一个音符
    if (elapsed >= music->duration[current_idx]) {
        current_idx++;

        if (current_idx >= music->length) {
            // 音乐播放完毕
            enhanced_state.is_playing = false;
            beepUpdate(0, 0); // 停止声音
            return false;
        }

        enhanced_state.note_index = current_idx;
        enhanced_state.note_start_time = enhanced_state.current_time;

        // 设置音符过渡
        uint16_t prev_note = music->note[current_idx - 1];
        uint16_t curr_note = music->note[current_idx];
        uint16_t prev_volume = music->volume[current_idx - 1];
        uint16_t curr_volume = music->volume[current_idx];

        if (music->transition[current_idx] && music->mode == MUSIC_MODE_SMOOTH) {
            smooth_note_transition(prev_note, curr_note, prev_volume, curr_volume, MUSIC_TRANSITION_STEPS);
        } else {
            beepUpdate(curr_volume, BeepCode[curr_note]);
            music_state.current_note = curr_note;
            music_state.current_volume = curr_volume;
            music_state.is_transitioning = false;
        }
    }

    // 更新音量包络
    if (music->envelope != ENVELOPE_NONE) {
        uint16_t base_volume = music->volume[current_idx];
        uint16_t envelope_volume = calculate_envelope(base_volume, music->envelope,
                                                     elapsed, music->duration[current_idx]);

        if (music_state.is_transitioning) {
            update_transition();
            beepUpdate(envelope_volume, music_state.current_note); // current_note现在存储频率值
        } else {
            beepUpdate(envelope_volume, BeepCode[music->note[current_idx]]);
        }
    } else if (music_state.is_transitioning) {
        update_transition();
        beepUpdate(music_state.current_volume, music_state.current_note); // current_note现在存储频率值
    }

    return true;
}

// 创建信号量
SemaphoreHandle_t dataSemaphore;

void send_music(uint8_t music_code , uint8_t music_switch)
{
    if(music_switch == 1)
    {
        commandState = music_code;
        xSemaphoreGive(dataSemaphore);    // 将信号量置起
    }
}

// 发送增强音乐
void send_enhanced_music(uint8_t music_code, uint8_t music_switch)
{
    if (music_switch == 1) {
        // 检查是否为增强音乐范围
        if (music_code >= MUSIC_ARMED && music_code <= MUSIC_RADIO_LOSS) {
            play_enhanced_music(music_code);
        } else {
            // 使用传统音乐播放
            send_music(music_code, music_switch);
        }
    }
}

static void music_loop(void)
{
    static uint16_t musicNum = 0;
    static BeepSound_t *musicSound;
    static uint32_t last_update_time = 0;

    // 优先处理增强音乐
    if (enhanced_state.is_playing) {
        uint32_t current_time = HAL_GetTick();

        // 根据音乐模式调整更新频率
        uint16_t update_interval = MUSIC_MIN_DELAY;
        if (enhanced_state.current_music) {
            switch (enhanced_state.current_music->mode) {
                case MUSIC_MODE_SMOOTH:
                case MUSIC_MODE_LEGATO:
                    update_interval = MUSIC_MIN_DELAY; // 高频更新以实现平滑过渡
                    break;
                case MUSIC_MODE_STACCATO:
                    update_interval = MUSIC_MIN_DELAY + 10;
                    break;
                default:
                    update_interval = 40; // 默认40ms
                    break;
            }
        }

        if (current_time - last_update_time >= update_interval) {
            update_enhanced_music();
            last_update_time = current_time;
        }

        osDelay(update_interval);
        return;
    }

    // 传统音乐播放逻辑
    if (xSemaphoreTake(dataSemaphore, 0) == pdTRUE) { // 非阻塞获取信号量
        for (;;) {
            // log_a("music:%d",commandState);
            if (musicNum == 0) {
                if (commandState == QUIET) {
                    // log_a("STOP MUSIC");
                    beepUpdate(0, 0); // 确保停止声音
                    break;
                }
                else {
                    // 检查音乐索引范围
                    uint8_t music_idx = commandState - 1;
                    if (music_idx < (sizeof(beepNewSound) / sizeof(beepNewSound[0]))) {
                        musicSound = &beepNewSound[music_idx];
                        prepareBeepPacket(musicSound);
                        musicNum++;
                        commandState = 0;
                    } else {
                        break; // 无效的音乐索引
                    }
                }
            } else {
                beepUpdate(frequencyCode.volume[musicNum - 1], frequencyCode.note[musicNum - 1]);
                musicNum++;
                if (musicNum > musicSound->lenght) {
                    musicNum = 0;
                    beepUpdate(0, 0); // 播放完毕，停止声音
                }
            }
            osDelay(40);
        }
    } else {
        // 没有音乐播放时的短暂延迟
        osDelay(50);
    }
}

// 音乐系统测试函数
void test_buzzer_basic(void)
{
    // 测试基本音符播放
    for (int i = 0; i < 21; i++) {
        beepUpdate(H_NOISE, BeepCode[i]);
        osDelay(300);
        beepUpdate(0, 0); // 停止
        osDelay(100);
    }
}

// 测试音量变化
void test_buzzer_volume(void)
{
    uint16_t test_note = BeepCode[ALTO_DO]; // 中音Do

    // 测试不同音量
    uint16_t volumes[] = {L_NOISE, M_NOISE, H_NOISE, MAX_NOISE};
    for (int i = 0; i < 4; i++) {
        beepUpdate(volumes[i], test_note);
        osDelay(500);
        beepUpdate(0, 0);
        osDelay(200);
    }
}

void music_task(void const *argument)
{
    //wait time for task init
    osDelay(MUSIC_TASK_INIT_TIME);

    HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_1);   //启动蜂鸣器定时器

    commandState = 0;//MUSIC_CHARGE + 1;
    dataSemaphore = xSemaphoreCreateBinary();

    // 启动时播放测试音，验证蜂鸣器工作
    osDelay(1000); // 等待系统稳定

    // 播放启动测试音
    beepUpdate(H_NOISE, BeepCode[ALTO_DO]);
    osDelay(200);
    beepUpdate(H_NOISE, BeepCode[ALTO_MI]);
    osDelay(200);
    beepUpdate(H_NOISE, BeepCode[ALTO_SOL]);
    osDelay(300);
    beepUpdate(0, 0); // 停止

    for(;;)
    {
        music_loop(); // call
    }
}
