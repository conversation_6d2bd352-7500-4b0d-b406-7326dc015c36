#ifndef MUCSIC_TASK_H
#define MUCSIC_TASK_H
#include "struct_typedef.h"
#include "cmsis_os.h"
#include <stdio.h>
#include <stdarg.h>
#include "string.h"
#include "main.h"
#define BUZZER_ENABLE 1  //觉得太吵了就关掉蜂鸣器吧！

#define MUSIC_TASK_INIT_TIME 5

#define constrainInt(v, lo, hi)	    (((int)(v) < (int)(lo)) ? (int)(lo) : (((int)(v) > (int)(hi)) ? (int)(hi) : (int)(v)))
#define BEEP_LENGTH 50
#define BEEP_PRESCALER 96-1
#define BEEP_MAX_NOISE 85  // 最大PWM占空比85%，避免过载
#define BEEP_MIN_NOISE 0   // 最小PWM占空比0%
#define BEEP_NOTE_MIN 10
#define BEEP_NOTE_MAX 32768 
#define MUSIC_MAX_LENGHT 48
#define MUSIC_TRANSITION_STEPS 8  // 音符过渡步数
#define MUSIC_MIN_DELAY 20        // 最小延迟时间(ms)
#define MUSIC_MAX_DELAY 200       // 最大延迟时间(ms)

// 音乐播放模式
typedef enum {
    MUSIC_MODE_NORMAL = 0,    // 普通模式
    MUSIC_MODE_SMOOTH,        // 平滑过渡模式
    MUSIC_MODE_STACCATO,      // 断奏模式
    MUSIC_MODE_LEGATO         // 连奏模式
} music_mode_t;

// 音量包络类型
typedef enum {
    ENVELOPE_NONE = 0,        // 无包络
    ENVELOPE_LINEAR,          // 线性包络
    ENVELOPE_EXPONENTIAL,     // 指数包络
    ENVELOPE_ADSR            // ADSR包络
} envelope_type_t;

enum{			//声音种类
	QUIET=0,
	MUSIC_ARMED,					//解锁提示音
	MUSIC_DISARMED,				//上锁提示音
	MUSIC_IMUCALI,				//校准提示音
	MUSIC_PARAMCALI,			//参数保存提示音
	MUSIC_MAGCALI,				//MAG校准
	MUSIC_RADIO_LOSS,			//失控
	MUSIC_TYPE_INFANTRY,
	MUSIC_TYPE_TANK,
	MUSIC_TYPE_AUXILIARY,
	MUSIC_TYPE_SENTRY,
	MUSIC_TYPE_UAV,
	MUSIC_NO_ID,
	MUSIC_LOWPOWER,
	MUSIC_CHARGE,
	MUSIC_LIST
};

enum{     //音阶
	BASS_DO=1,
	BASS_RE,
	BASS_MI,
	BASS_FA,
	BASS_SOL,
	BASS_LA,
	BASS_SI,

	ALTO_DO,
	ALTO_RE,
	ALTO_MI,
	ALTO_FA,
	ALTO_SOL,
	ALTO_LA,
	ALTO_SI,

	HIGH_DO,
	HIGH_RE,
	HIGH_MI,
	HIGH_FA,
	HIGH_SOL,
	HIGH_LA,
	HIGH_SI,

	// 超高音区 - 用于马里奥风格音效
	SUPER_HIGH_DO,
	SUPER_HIGH_RE,
	SUPER_HIGH_MI,
	SUPER_HIGH_FA,
	SUPER_HIGH_SOL,
	SUPER_HIGH_LA,
	SUPER_HIGH_SI,
};
enum{     //音阶
  M_DO = 1,
	M_RE,
	M_MI,
	M_FA,
	M_SOL,
	M_LA,
	M_SI,
};

//音量 - 重新调整音量值以获得更好的音效
#define	N_NOISE 0      // 静音
#define L_NOISE 15     // 低音量 (15% PWM占空比)
#define M_NOISE 35     // 中音量 (35% PWM占空比)
#define	H_NOISE 60     // 高音量 (60% PWM占空比)
#define MAX_NOISE 85   // 最大音量 (85% PWM占空比，避免过载)

// 新增音乐结构体，支持更丰富的音乐表现
typedef struct {
    uint16_t note[MUSIC_MAX_LENGHT];        // 音符序列
    uint16_t volume[MUSIC_MAX_LENGHT];      // 音量序列
    uint16_t duration[MUSIC_MAX_LENGHT];    // 每个音符的持续时间(ms)
    uint16_t transition[MUSIC_MAX_LENGHT];  // 过渡类型(0=直接跳转, 1=平滑过渡)
    uint16_t length;                        // 音符数量
    music_mode_t mode;                      // 播放模式
    envelope_type_t envelope;               // 音量包络类型
    uint16_t tempo;                         // 节拍(BPM)
} enhanced_music_t;

enum{
    MUSIC_TYPE_PASS = 1,
    MUSCI_TYPE_BUTTON ,
    MUSIC_TYPE_SUCCESS,
    MUSIC_TYPE_FAIL,
};

extern void music_task(void const * argument);

extern void send_music(uint8_t music_code , uint8_t music_switch);
extern void send_enhanced_music(uint8_t music_code, uint8_t music_switch);
extern void play_enhanced_music(uint8_t music_index);
extern bool_t update_enhanced_music(void);
extern uint16_t calculate_envelope(uint16_t base_volume, envelope_type_t envelope, uint16_t time_ms, uint16_t duration_ms);
extern void smooth_note_transition(uint16_t from_note, uint16_t to_note, uint16_t from_volume, uint16_t to_volume, uint16_t steps);

// 测试和调试函数
extern void test_buzzer_basic(void);
extern void test_buzzer_volume(void);
extern void test_mario_sounds(void);
extern void beepUpdate(uint16_t beepSound, uint16_t note);

extern SemaphoreHandle_t dataSemaphore;
#endif
