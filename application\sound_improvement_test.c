/**
 * @file sound_improvement_test.c
 * @brief 音效改进测试程序 - 验证音量和余韵效果
 * <AUTHOR> Assistant
 * @date 2025-07-30
 */

#include "music_task.h"
#include "music_examples.h"
#include "cmsis_os.h"
#include "main.h"

/**
 * @brief 对比测试：原始音效 vs 改进音效
 */
void compare_sound_effects(void)
{
    // 测试1: 原始简单音效（无余韵）
    beepUpdate(MAX_NOISE, BeepCode[HIGH_DO]);
    osDelay(200);
    beepUpdate(0, 0);
    osDelay(500);
    
    // 测试2: 改进音效（带余韵）
    beepUpdateWithFade(MAX_NOISE, BeepCode[HIGH_DO], 200);
    osDelay(500);
    
    // 测试3: 原始金币音效
    beepUpdate(MAX_NOISE, BeepCode[HIGH_MI]);
    osDelay(100);
    beepUpdate(0, 0);
    osDelay(50);
    beepUpdate(MAX_NOISE, BeepCode[HIGH_SOL]);
    osDelay(100);
    beepUpdate(0, 0);
    osDelay(500);
    
    // 测试4: 改进金币音效
    play_mario_coin_sound();
    osDelay(1000);
}

/**
 * @brief 音量级别测试
 */
void test_all_volume_levels(void)
{
    uint16_t test_note = BeepCode[HIGH_DO];
    
    // 测试所有音量级别，每个都带余韵
    beepUpdateWithFade(L_NOISE, test_note, 300);
    osDelay(500);
    
    beepUpdateWithFade(M_NOISE, test_note, 300);
    osDelay(500);
    
    beepUpdateWithFade(H_NOISE, test_note, 300);
    osDelay(500);
    
    beepUpdateWithFade(MAX_NOISE, test_note, 300);
    osDelay(500);
}

/**
 * @brief 余韵效果测试 - 不同持续时间
 */
void test_fade_durations(void)
{
    uint16_t test_note = BeepCode[HIGH_MI];
    uint16_t durations[] = {100, 200, 300, 500, 800};
    
    for (int i = 0; i < 5; i++) {
        beepUpdateWithFade(MAX_NOISE, test_note, durations[i]);
        osDelay(durations[i] + 200); // 等待音效完全结束
    }
}

/**
 * @brief 频率测试 - 测试不同音符的清晰度
 */
void test_frequency_clarity(void)
{
    // 测试低音、中音、高音、超高音的清晰度
    uint16_t test_notes[] = {
        BASS_DO, ALTO_DO, HIGH_DO, SUPER_HIGH_DO,
        BASS_MI, ALTO_MI, HIGH_MI, SUPER_HIGH_MI,
        BASS_SOL, ALTO_SOL, HIGH_SOL, SUPER_HIGH_SOL
    };
    
    for (int i = 0; i < 12; i++) {
        beepUpdateWithFade(MAX_NOISE, BeepCode[test_notes[i]], 250);
        osDelay(400);
    }
}

/**
 * @brief 马里奥经典音效序列测试
 */
void test_mario_classic_sequence(void)
{
    // 1. 跳跃音效
    beepUpdateWithFade(H_NOISE, BeepCode[HIGH_DO], 120);
    osDelay(50);
    beepUpdateWithFade(MAX_NOISE, BeepCode[HIGH_MI], 150);
    osDelay(50);
    beepUpdateWithFade(MAX_NOISE, BeepCode[HIGH_SOL], 200);
    osDelay(300);
    
    // 2. 金币音效
    play_mario_coin_sound();
    osDelay(500);
    
    // 3. 1UP音效
    play_mario_1up_sound();
    osDelay(500);
    
    // 4. 成功音效（使用传统接口）
    send_music(MUSIC_TYPE_SUCCESS, 1);
    osDelay(2000);
}

/**
 * @brief 音效响应时间测试
 */
void test_sound_response_time(void)
{
    uint32_t start_time, end_time;
    
    // 测试音效启动时间
    start_time = HAL_GetTick();
    beepUpdate(MAX_NOISE, BeepCode[HIGH_DO]);
    end_time = HAL_GetTick();
    
    osDelay(200);
    beepUpdate(0, 0);
    osDelay(300);
    
    // 测试余韵音效启动时间
    start_time = HAL_GetTick();
    beepUpdateWithFade(MAX_NOISE, BeepCode[HIGH_DO], 200);
    end_time = HAL_GetTick();
    
    osDelay(500);
}

/**
 * @brief 音效质量评估测试
 */
void test_sound_quality_assessment(void)
{
    // 测试1: 清晰度测试 - 快速音符序列
    uint16_t clarity_notes[] = {HIGH_DO, HIGH_RE, HIGH_MI, HIGH_FA, HIGH_SOL};
    for (int i = 0; i < 5; i++) {
        beepUpdateWithFade(MAX_NOISE, BeepCode[clarity_notes[i]], 150);
        osDelay(50);
    }
    osDelay(500);
    
    // 测试2: 音量一致性测试
    for (int i = 0; i < 3; i++) {
        beepUpdateWithFade(MAX_NOISE, BeepCode[HIGH_DO], 200);
        osDelay(300);
    }
    osDelay(500);
    
    // 测试3: 频率稳定性测试
    beepUpdateWithFade(MAX_NOISE, BeepCode[HIGH_DO], 1000); // 长音测试
    osDelay(1200);
}

/**
 * @brief 完整的音效改进验证测试
 */
void complete_sound_improvement_test(void)
{
    // 1. 对比测试
    compare_sound_effects();
    osDelay(1000);
    
    // 2. 音量测试
    test_all_volume_levels();
    osDelay(1000);
    
    // 3. 余韵测试
    test_fade_durations();
    osDelay(1000);
    
    // 4. 频率测试
    test_frequency_clarity();
    osDelay(1000);
    
    // 5. 马里奥音效测试
    test_mario_classic_sequence();
    osDelay(1000);
    
    // 6. 响应时间测试
    test_sound_response_time();
    osDelay(1000);
    
    // 7. 质量评估测试
    test_sound_quality_assessment();
}

/**
 * @brief 快速音效验证 - 用于日常测试
 */
void quick_sound_verification(void)
{
    // 快速播放几个关键音效
    beepUpdateWithFade(MAX_NOISE, BeepCode[HIGH_DO], 200);
    osDelay(300);
    
    play_mario_coin_sound();
    osDelay(500);
    
    play_mario_1up_sound();
    osDelay(500);
    
    // 测试音量
    beepUpdateWithFade(MAX_NOISE, BeepCode[SUPER_HIGH_DO], 300);
    osDelay(500);
}

/**
 * @brief 音效问题诊断
 */
void diagnose_sound_issues(void)
{
    // 1. 基础硬件测试
    beepUpdate(MAX_NOISE, BeepCode[HIGH_DO]);
    osDelay(500);
    beepUpdate(0, 0);
    osDelay(300);
    
    // 2. 音量范围测试
    test_volume_levels();
    osDelay(500);
    
    // 3. 频率范围测试
    beepUpdateWithFade(MAX_NOISE, BeepCode[BASS_DO], 200);
    osDelay(300);
    beepUpdateWithFade(MAX_NOISE, BeepCode[ALTO_DO], 200);
    osDelay(300);
    beepUpdateWithFade(MAX_NOISE, BeepCode[HIGH_DO], 200);
    osDelay(300);
    beepUpdateWithFade(MAX_NOISE, BeepCode[SUPER_HIGH_DO], 200);
    osDelay(300);
    
    // 4. 余韵效果测试
    beepUpdateWithFade(MAX_NOISE, BeepCode[HIGH_MI], 400);
    osDelay(600);
}
