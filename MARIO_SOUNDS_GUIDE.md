# 超级马里奥风格音效系统使用指南

## 概述

本系统将原有的简单"滴滴"音效升级为超级马里奥风格的欢快高频音效，具有以下特点：

- 🎵 **高频清脆**：使用高音区音符，声音清脆悦耳
- ⚡ **快节奏**：快速播放，节奏欢快
- 🎼 **旋律丰富**：多种音效组合，表现力强
- 🔊 **动态音量**：音量变化自然，避免单调

## 音效类型

### 1. 基本交互音效

#### 按键音效 - 马里奥跳跃音
```c
play_button_sound();        // 原有接口，已升级为马里奥风格
play_mario_jump();          // 直接调用马里奥跳跃音效
```
**音效特点**：`滴~滴~滴~` (Do-Mi-Sol上行)
**使用场景**：按键反馈、界面操作

#### 成功音效 - 马里奥金币音
```c
play_success_sound();       // 原有接口，已升级
play_mario_coin_sound();    // 直接调用金币音效
```
**音效特点**：`滴滴滴滴~` (快速上行音阶)
**使用场景**：操作成功、任务完成

#### 失败音效 - 马里奥死亡音
```c
play_failure_sound();       // 原有接口，已升级
play_mario_death();         // 直接调用死亡音效
```
**音效特点**：下行悲伤音调，音量逐渐减弱
**使用场景**：操作失败、错误提示

### 2. 系统状态音效

#### 启动音效 - 马里奥开机音
```c
play_startup_music();       // 系统启动音乐
send_enhanced_music(MUSIC_ARMED, 1);
```
**音效特点**：`滴~滴~滴~滴~` (欢快上行)
**使用场景**：系统启动、设备解锁

#### 关闭音效 - 马里奥进入管道音
```c
play_shutdown_music();      // 系统关闭音乐
send_enhanced_music(MUSIC_DISARMED, 1);
```
**音效特点**：`滴~滴~滴~` (温和下行)
**使用场景**：系统关闭、设备锁定

### 3. 特殊功能音效

#### 校准音效 - 马里奥升级音
```c
play_calibration_music();   // 校准过程音乐
send_enhanced_music(MUSIC_IMUCALI, 1);
```
**音效特点**：`滴滴滴滴滴滴滴~` (快速上行音阶)
**使用场景**：设备校准、参数调整

#### 保存音效 - 马里奥获得星星音
```c
send_enhanced_music(MUSIC_PARAMCALI, 1);
```
**音效特点**：`滴~滴~滴~滴~滴~` (和谐上行)
**使用场景**：数据保存、设置完成

#### 警告音效 - 马里奥紧急警报
```c
play_emergency_sound();     // 紧急警告
send_enhanced_music(MUSIC_RADIO_LOSS, 1);
```
**音效特点**：`滴~滴~滴~` (高低音快速交替)
**使用场景**：系统警告、紧急状态

### 4. 高级音效

#### 1UP音效 - 马里奥额外生命
```c
play_mario_1up_sound();
```
**音效特点**：六个音符的上行旋律，持续时间较长
**使用场景**：重要成就、特殊奖励

#### 城堡完成音效 - 马里奥胜利音
```c
play_mario_castle_complete();
```
**音效特点**：完整的胜利音阶，最后有长音
**使用场景**：任务完成、测试通过

## 技术参数

### 音频配置
- **定时器**：TIM8
- **PWM频率**：10kHz基频
- **音量范围**：0-85% PWM占空比
- **频率范围**：130Hz - 2000Hz+

### 音符定义
```c
// 低音区 (C3-B3)
BASS_DO, BASS_RE, BASS_MI, BASS_FA, BASS_SOL, BASS_LA, BASS_SI

// 中音区 (C4-B4) 
ALTO_DO, ALTO_RE, ALTO_MI, ALTO_FA, ALTO_SOL, ALTO_LA, ALTO_SI

// 高音区 (C5-B5)
HIGH_DO, HIGH_RE, HIGH_MI, HIGH_FA, HIGH_SOL, HIGH_LA, HIGH_SI

// 超高音区 (C6-B6) - 马里奥风格专用
SUPER_HIGH_DO, SUPER_HIGH_RE, SUPER_HIGH_MI, SUPER_HIGH_FA, 
SUPER_HIGH_SOL, SUPER_HIGH_LA, SUPER_HIGH_SI
```

### 音量级别
```c
#define N_NOISE 0      // 静音
#define L_NOISE 15     // 低音量 (15% PWM)
#define M_NOISE 35     // 中音量 (35% PWM)  
#define H_NOISE 60     // 高音量 (60% PWM)
#define MAX_NOISE 85   // 最大音量 (85% PWM)
```

## 使用示例

### 基本使用
```c
#include "music_examples.h"
#include "mario_sounds_test.h"

// 按键反馈
void on_button_press(void) {
    play_mario_jump();
}

// 操作成功
void on_operation_success(void) {
    play_mario_coin_sound();
}

// 系统启动
void on_system_startup(void) {
    play_startup_music();
}
```

### 高级使用
```c
// 完整的马里奥音效演示
void demo_all_sounds(void) {
    mario_sounds_demo();
}

// 交互式音效测试
void test_specific_sound(uint8_t type) {
    play_mario_sound_by_type(type);
}

// 自定义音效序列
void custom_sound_sequence(void) {
    play_mario_jump();
    osDelay(200);
    play_mario_coin_sound();
    osDelay(300);
    play_mario_1up_sound();
}
```

## 调试和测试

### 硬件测试
```c
test_buzzer_hardware();     // 测试蜂鸣器硬件
test_mario_sounds();        // 测试马里奥音效
music_system_diagnosis();   // 完整系统诊断
```

### 性能测试
```c
test_music_performance();   // 性能测试
test_music_quality();       // 音质测试
```

## 配置选项

### 启用/禁用蜂鸣器
```c
#define BUZZER_ENABLE 1  // 1=启用, 0=禁用
```

### 调整音效参数
```c
#define MUSIC_TRANSITION_STEPS 8    // 音符过渡步数
#define MUSIC_MIN_DELAY 20          // 最小延迟时间
#define MUSIC_MAX_DELAY 200         // 最大延迟时间
```

## 故障排除

### 常见问题

1. **声音太小**
   - 检查音量设置：使用H_NOISE或MAX_NOISE
   - 检查硬件连接：确认蜂鸣器正确连接到TIM8_CH1

2. **声音不清晰**
   - 检查频率设置：确认BeepCode数组值正确
   - 检查PWM配置：确认TIM8配置正确

3. **没有声音**
   - 检查BUZZER_ENABLE宏定义
   - 检查定时器启动：确认HAL_TIM_PWM_Start(&htim8, TIM_CHANNEL_1)已调用

### 调试步骤

1. 调用`test_buzzer_hardware()`测试硬件
2. 调用`test_mario_sounds()`测试音效
3. 检查系统配置和引脚连接
4. 使用示波器检查PWM输出

## 总结

马里奥风格音效系统提供了丰富、欢快的音频反馈，显著提升了用户体验。通过合理使用不同类型的音效，可以让设备的交互更加生动有趣。

**推荐配置**：
- 按键反馈：`play_mario_jump()`
- 成功提示：`play_mario_coin_sound()`
- 失败提示：`play_mario_death()`
- 系统启动：`play_startup_music()`
- 重要成就：`play_mario_1up_sound()`
